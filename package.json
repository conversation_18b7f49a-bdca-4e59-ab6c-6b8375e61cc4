{"name": "ai-tender-analysis", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "format": "prettier --write \"src/**/*.{ts,html,scss,css,json}\"", "prepare": "husky && husky install", "lint": "ng lint", "lint:fix": "ng lint --fix", "i18n:extract": "ngx-translate-extract --input './src/app/!(api)/**/*.{ts,html}' --output ./public/i18n/en.json ./public/i18n/de.json ./public/i18n/fi.json --merge --key-as-default-value --format json && node scripts/flattenToNested.mjs", "i18n:extract-en": "ngx-translate-extract --input './src/app/!(api)/**/*.{ts,html}' --output ./public/i18n/en.json --merge --key-as-default-value --format json && node scripts/flattenToNested.mjs", "generate:api": "openapi-generator-cli generate -i ./openapi/aita-api.yaml -g typescript-angular -o ./src/app/api --global-property=skipFormModel=false --additional-properties=npmName=@gofore.aita/api,ngVersion=19,serviceSuffix=ApiService,enumPropertyNaming=original"}, "private": true, "dependencies": {"@angular-eslint/schematics": "^19.0.2", "@angular/animations": "^19.0.0", "@angular/cdk": "^19.0.1", "@angular/common": "^19.0.0", "@angular/compiler": "^19.0.0", "@angular/core": "^19.0.0", "@angular/forms": "^19.0.0", "@angular/material": "^19.0.1", "@angular/platform-browser": "^19.0.0", "@angular/platform-browser-dynamic": "^19.0.0", "@angular/router": "^19.0.0", "@azure/msal-angular": "^4.0.2", "@azure/msal-browser": "^4.0.2", "@mdi/angular-material": "^7.2.96", "@ngx-translate/core": "^16.0.4", "@ngx-translate/http-loader": "^16.0.1", "file-saver": "^2.0.5", "jszip": "^3.10.1", "marked": "^15.0.12", "ngx-markdown": "^19.1.1", "rxjs": "^7.8.0", "tslib": "^2.3.0", "zone.js": "^0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.0.2", "@angular/cli": "^19.0.2", "@angular/compiler-cli": "^19.0.0", "@angular/language-service": "^19.0.5", "@types/file-saver": "^2.0.7", "@types/jasmine": "^5.1.0", "@vendure/ngx-translate-extract": "^9.4.0", "angular-eslint": "19.0.2", "autoprefixer": "^10.4.20", "eslint": "^9.16.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-simple-import-sort": "^12.1.1", "flat": "^6.0.1", "globals": "^15.14.0", "husky": "^8.0.0", "jasmine-core": "^5.4.0", "karma": "^6.4.0", "karma-chrome-launcher": "^3.2.0", "karma-coverage": "^2.2.0", "karma-jasmine": "^5.1.0", "karma-jasmine-html-reporter": "^2.1.0", "lint-staged": "^15.3.0", "postcss": "^8.4.49", "prettier": "^3.4.2", "tailwindcss": "^3.4.17", "typescript": "^5.6.2", "typescript-eslint": "8.18.0"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{ts,html,css,scss}": ["prettier --write", "eslint --fix"]}}