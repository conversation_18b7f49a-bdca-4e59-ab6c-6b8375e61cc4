import { Form<PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms'

import { TenderDTO, WorkflowStatusDTO } from '@app/api'
import { TenderMetadataFileItem } from '@modules/tenders/models/tender-file.model'
import {
    iso8601DatetimeValidator,
    iso8601DateValidator,
    onlyNumbersValidator,
    validUrlValidator,
} from '@shared/utils/custom-form-validation/custom-validators'

import {
    TenderCreateFormControlModel,
    TenderCreateFormModel,
    TenderFileFormControlModel,
    TenderFileFormModel,
} from './tender.model'

export function buildTenderCreateForm(
    formBuilder: FormBuilder,
    includeFiles = true
): FormGroup<TenderCreateFormControlModel> {
    const groupConfig: any = {
        title: formBuilder.control('', {
            validators: [Validators.required],
            nonNullable: true,
        }),
        sourceUrl: formBuilder.control('', {
            validators: [validUrlValidator()],
            nonNullable: true,
        }),
        client: formBuilder.control('', {
            validators: [Validators.required],
            nonNullable: true,
        }),
        submissionDate: formBuilder.control('', {
            validators: [Validators.required, iso8601DatetimeValidator()],
            nonNullable: true,
        }),
        bindingDeadline: formBuilder.control('', {
            validators: [Validators.required, iso8601DateValidator()],
            nonNullable: true,
        }),
        contractDuration: formBuilder.control('', {
            validators: [Validators.required],
            nonNullable: true,
        }),
        publicationDate: formBuilder.control('', {
            validators: [Validators.required, iso8601DateValidator()],
            nonNullable: true,
        }),
        questionDeadline: formBuilder.control('', {
            validators: [Validators.required, iso8601DatetimeValidator()],
            nonNullable: true,
        }),
        contractValue: formBuilder.control<number | null>(null, {
            validators: [onlyNumbersValidator()],
        }),
        maximumBudget: formBuilder.control<string | null>(null),
        winningCriteria: formBuilder.control('', {
            validators: [Validators.required],
            nonNullable: true,
        }),
        weightingPriceQuality: formBuilder.control('', {
            validators: [Validators.required],
            nonNullable: true,
        }),
        deliveryLocation: formBuilder.control('', {
            validators: [Validators.required],
            nonNullable: true,
        }),
        description: formBuilder.control('', {
            validators: [Validators.required],
            nonNullable: true,
        }),
        comment: formBuilder.control<string | null>(null),
        rating: formBuilder.control<number | null>(null),
        isFavorite: formBuilder.control<boolean>(false),
        workflowStatus: formBuilder.control<WorkflowStatusDTO>('New'),
    }

    if (includeFiles) {
        groupConfig.files = formBuilder.control([])
    }

    return formBuilder.group<TenderCreateFormControlModel>(groupConfig)
}

export function buildTenderFileForm(
    formBuilder: FormBuilder
): FormGroup<TenderFileFormControlModel> {
    return formBuilder.group<TenderFileFormControlModel>({
        files: formBuilder.control({ newFiles: [], metadataFiles: [] }),
    })
}

export function mapTenderDTOToFormModel(
    tender: TenderDTO
): TenderCreateFormModel {
    return {
        title: tender.title,
        sourceUrl: tender.sourceUrl,
        client: tender.client,
        submissionDate: tender.submissionDate,
        bindingDeadline: tender.bindingDeadline,
        contractDuration: tender.contractDuration,
        publicationDate: tender.publicationDate,
        questionDeadline: tender.questionDeadline,
        contractValue: tender.contractValue ?? null,
        maximumBudget: tender.maximumBudget ?? null,
        winningCriteria: tender.winningCriteria,
        weightingPriceQuality: tender.weightingPriceQuality,
        deliveryLocation: tender.deliveryLocation,
        description: tender.description,
        workflowStatus: tender.workflowStatus,
        comment: tender.comment ?? null,
        rating: tender.rating ?? null,
        isFavorite: tender.isFavorite ?? false,
        files: {
            newFiles: [],
            metadataFiles: [],
        },
    }
}

export function mapTenderDTOToTenderFileFormModel(
    tender: TenderDTO
): TenderFileFormModel {
    const metadataFiles: TenderMetadataFileItem[] = tender.files
        ? tender.files.map((meta) => ({
              metadata: meta,
              tenderId: tender.id,
              progress: 100,
              selected: false,
          }))
        : []
    return {
        files: {
            newFiles: [],
            metadataFiles: metadataFiles,
        },
    }
}
