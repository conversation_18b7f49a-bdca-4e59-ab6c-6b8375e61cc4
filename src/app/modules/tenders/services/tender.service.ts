import { Injectable } from '@angular/core'

import {
    CollectionSortDirectionDTO,
    TenderCreateDTO,
    TenderCreateSimpleDTO,
    TenderDTO,
    TenderPagedDTO,
    TenderSortableFieldsDTO,
    TenderUpdateDTO,
} from '@app/api'
import { TenderApiService } from '@app/api/api/tender.service'
import { ApiErrorHandlerService } from '@shared/services/api-error-handler.service'

import { Observable } from 'rxjs'

@Injectable({
    providedIn: 'root',
})
export class TenderService {
    constructor(
        private tenderService: TenderApiService,
        private apiErrorHandler: ApiErrorHandlerService
    ) {}

    createTender(tender: TenderCreateDTO): Observable<TenderDTO> {
        return this.tenderService
            .createTender(
                tender.title,
                tender.client,
                tender.submissionDate,
                tender.bindingDeadline,
                tender.contractDuration,
                tender.publicationDate,
                tender.questionDeadline,
                tender.winningCriteria,
                tender.weightingPriceQuality,
                tender.deliveryLocation,
                tender.description,
                tender.workflowStatus,
                tender.sourceUrl,
                tender.contractValue,
                tender.maximumBudget,
                tender.comment,
                tender.rating,
                tender.isFavorite,
                tender.files
            )
            .pipe(this.apiErrorHandler.handleError<TenderDTO>('createTender'))
    }

    createTenderWithAI(
        simpleTender: TenderCreateSimpleDTO
    ): Observable<TenderDTO> {
        const { title, sourceUrl, description, files } = simpleTender
        return this.tenderService
            .createTenderWithAI(title, description, sourceUrl, files)
            .pipe(
                this.apiErrorHandler.handleError<TenderDTO>(
                    'createTenderWithAI'
                )
            )
    }

    updateTender(id: string, tender: TenderUpdateDTO): Observable<TenderDTO> {
        return this.tenderService
            .updateTender(id, tender)
            .pipe(this.apiErrorHandler.handleError<TenderDTO>('updateTender'))
    }

    deleteTender(id: string): Observable<TenderDTO> {
        return this.tenderService
            .deleteTender(id)
            .pipe(this.apiErrorHandler.handleError<TenderDTO>('deleteTender'))
    }

    getAllTenders(
        page: number,
        size: number,
        sortBy?: TenderSortableFieldsDTO,
        sortDirection?: CollectionSortDirectionDTO
    ): Observable<TenderPagedDTO> {
        return this.tenderService
            .getAllTenders(page, size, sortBy, sortDirection)
            .pipe(
                this.apiErrorHandler.handleError<TenderPagedDTO>(
                    'getAllTenders'
                )
            )
    }

    getTenderById(id: string): Observable<TenderDTO> {
        return this.tenderService
            .getTenderById(id)
            .pipe(this.apiErrorHandler.handleError<TenderDTO>('getTenderById'))
    }
}
