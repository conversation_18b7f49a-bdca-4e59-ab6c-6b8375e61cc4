import {
    ChangeDetectorRef,
    Component,
    inject,
    input,
    OnChang<PERSON>,
    SimpleChang<PERSON>,
} from '@angular/core'
import { <PERSON><PERSON><PERSON>er, ReactiveFormsModule } from '@angular/forms'
import { MatIconButton } from '@angular/material/button'
import { MatCheckbox } from '@angular/material/checkbox'
import { MatIcon } from '@angular/material/icon'

import { TenderDTO, TenderUpdateDTO } from '@app/api'
import { LoggerService } from '@core/logging/logger.service'
import { TenderCreateFormModel } from '@modules/tenders/models/tender.model'
import {
    buildTenderCreateForm,
    mapTenderDTOToFormModel,
} from '@modules/tenders/models/tender-form.factory'
import { TenderService } from '@modules/tenders/services/tender.service'
import { DatetimepickerFieldComponent } from '@shared/components/datetimepicker-field/datetimepicker-field.component'
import { InputFieldNumberComponent } from '@shared/components/input-field-number/input-field-number.component'
import { InputFieldTextComponent } from '@shared/components/input-field-text/input-field-text.component'
import { InputTextareaComponent } from '@shared/components/input-textarea/input-textarea.component'
import { SeparatorComponent } from '@shared/components/separator/separator.component'

import { TranslatePipe } from '@ngx-translate/core'
import { EMPTY, Observable } from 'rxjs'

@Component({
    selector: 'app-tender-update',
    imports: [
        InputFieldTextComponent,
        TranslatePipe,
        ReactiveFormsModule,
        SeparatorComponent,
        DatetimepickerFieldComponent,
        InputFieldNumberComponent,
        InputTextareaComponent,
        MatCheckbox,
        MatIcon,
        MatIconButton,
    ],
    templateUrl: './tender-update.component.html',
    standalone: true,
    styleUrl: './tender-update.component.scss',
})
export class TenderUpdateComponent implements OnChanges {
    tender = input<TenderDTO | undefined>(undefined)

    tenderDetailsForm = buildTenderCreateForm(inject(FormBuilder), false)

    private cdr = inject(ChangeDetectorRef)
    private tenderService = inject(TenderService)
    private logger = inject(LoggerService)

    get formValid(): boolean {
        return this.tenderDetailsForm.valid
    }
    isDirty(): boolean {
        return this.tenderDetailsForm.dirty
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['tender'] && this.tender) {
            this.resetForm(this.tender())
        }
    }

    onSave(): Observable<TenderDTO> {
        if (!this.tender) {
            this.logger.warn('tender was undefined')
            return EMPTY
        }

        if (!this.tenderDetailsForm.valid) {
            this.logger.debug('tenderDetailsForm was not valid')
            return EMPTY
        }

        const formValue = this.tenderDetailsForm.getRawValue()
        const modifiedTender: TenderUpdateDTO = {
            ...formValue,
            contractValue: formValue.contractValue ?? undefined,
            maximumBudget: formValue.maximumBudget ?? undefined,
            comment: formValue.comment ?? undefined,
            rating: formValue.rating ?? undefined,
            isFavorite: formValue.isFavorite,
            workflowStatus: formValue.workflowStatus,
        }

        return this.tenderService.updateTender(
            this.tender()!.id,
            modifiedTender
        )
    }

    resetForm(tender: TenderDTO | null | undefined) {
        if (!tender) {
            this.logger.warn('tenderId was undefined')
            return
        }

        const formModel: TenderCreateFormModel = mapTenderDTOToFormModel(tender)

        this.tenderDetailsForm.reset(formModel, { emitEvent: false })
        this.tenderDetailsForm.markAsPristine()

        setTimeout(() => {
            this.tenderDetailsForm.markAllAsTouched()
            this.tenderDetailsForm.updateValueAndValidity({ onlySelf: false })
            this.cdr.detectChanges()
        })
    }

    setRating(rating: number): void {
        const ratingControl = this.tenderDetailsForm.get('rating')
        if (ratingControl) {
            ratingControl.setValue(rating)
            ratingControl.markAsDirty()
            ratingControl.markAsTouched()
            this.cdr.markForCheck()
        }
    }
    // TODO
    // setWorkflowStatus(workflowStatus: WorkflowStatusDTO): void {
    //     const workflowStatusControl =
    //         this.tenderDetailsForm.get('workflowStatus')
    //     if (workflowStatusControl) {
    //         workflowStatusControl.setValue(workflowStatus)
    //         workflowStatusControl.markAsDirty()
    //         workflowStatusControl.markAsTouched()
    //         this.cdr.markForCheck()
    //     }
    // }
}
