/**
 * AI Tender Analysis - REST API
 *
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

/**
 * A response object that provides details the analysis of a tender
 */
export interface AnalysisResultDTO {
    /**
     * A result of the Tender analysis
     */
    analysisResult?: string
    /**
     * The point in time when the last analysis has been done (ISO 8601 date time format)
     */
    lastUpdatedTime?: string
    /**
     * Amount of tokens that have been used for the prompt to perform the analysis
     */
    promptTokens?: number
    /**
     * Amount of tokens that have been used for the generation of the result to perform the analysis
     */
    completionTokens?: number
}
