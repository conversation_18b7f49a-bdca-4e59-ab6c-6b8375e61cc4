/**
 * AI Tender Analysis - REST API
 *
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { WorkflowStatusDTO } from './workflowStatusDTO'

/**
 * A tender is a formal process where government agencies or public entities invite bids from suppliers or contractors to provide goods, services, or works.
 */
export interface TenderCreateDTO {
    /**
     * Title of the tender
     */
    title: string
    /**
     * Direct link to the original source of the tender
     */
    sourceUrl?: string
    /**
     * The publisher of the tender
     */
    client: string
    /**
     * The latest date and time until the offer can be submitted (ISO 8601 date time format)
     */
    submissionDate: string
    /**
     * The period of time during which a bidder is legally bound by its bid
     */
    bindingDeadline: string
    /**
     * Duration of the contract
     */
    contractDuration: string
    /**
     * Date when the tender was published (ISO 8601 date format)
     */
    publicationDate: string
    /**
     * The question deadline is the last datetime, questions can be sent to the client (ISO 8601 date time format)
     */
    questionDeadline: string
    /**
     * Value of the whole contract, the currency unit is Euro (€)
     */
    contractValue?: number
    /**
     * The maximum amount of budget, normally listed in \"Person-days\" (PT)
     */
    maximumBudget?: string
    /**
     * List of criteria we have to deliver to win the tender + optional topics that are relevant (Must have and nice to have criteria)
     */
    winningCriteria: string
    /**
     * The ratio between price and quality which is well described in the tender.
     */
    weightingPriceQuality: string
    /**
     * Location requirements for the delivery of a tender.
     */
    deliveryLocation: string
    /**
     * Very long general description text
     */
    description: string
    workflowStatus: WorkflowStatusDTO
    /**
     * User comments about the tender
     */
    comment?: string
    /**
     * User rating of the tender from 1 to 5
     */
    rating?: number
    /**
     * Flag indicating if the tender is marked as a favorite
     */
    isFavorite?: boolean
    /**
     * List of documents that belong to the tender and should be uploaded
     */
    files?: Array<Blob>
}
export namespace TenderCreateDTO {}
