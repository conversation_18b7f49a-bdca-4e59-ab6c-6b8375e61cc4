/**
 * AI Tender Analysis - REST API
 *
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Inject, Injectable, Optional } from '@angular/core'
import {
    HttpClient,
    HttpHeaders,
    HttpParams,
    HttpResponse,
    HttpEvent,
    HttpParameterCodec,
    HttpContext,
} from '@angular/common/http'
import { CustomHttpParameterCodec } from '../encoder'
import { Observable } from 'rxjs'

// @ts-ignore
import { CollectionSortDirectionDTO } from '../model/collectionSortDirectionDTO'
// @ts-ignore
import { ErrorDTO } from '../model/errorDTO'
// @ts-ignore
import { FileMetadataDTO } from '../model/fileMetadataDTO'
// @ts-ignore
import { TenderDTO } from '../model/tenderDTO'
// @ts-ignore
import { TenderPagedDTO } from '../model/tenderPagedDTO'
// @ts-ignore
import { TenderSortableFieldsDTO } from '../model/tenderSortableFieldsDTO'
// @ts-ignore
import { TenderUpdateDTO } from '../model/tenderUpdateDTO'
// @ts-ignore
import { WorkflowStatusDTO } from '../model/workflowStatusDTO'

// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS } from '../variables'
import { Configuration } from '../configuration'

@Injectable({
    providedIn: 'root',
})
export class TenderApiService {
    protected basePath = 'http://localhost'
    public defaultHeaders = new HttpHeaders()
    public configuration = new Configuration()
    public encoder: HttpParameterCodec

    constructor(
        protected httpClient: HttpClient,
        @Optional() @Inject(BASE_PATH) basePath: string | string[],
        @Optional() configuration: Configuration
    ) {
        if (configuration) {
            this.configuration = configuration
        }
        if (typeof this.configuration.basePath !== 'string') {
            const firstBasePath = Array.isArray(basePath)
                ? basePath[0]
                : undefined
            if (firstBasePath != undefined) {
                basePath = firstBasePath
            }

            if (typeof basePath !== 'string') {
                basePath = this.basePath
            }
            this.configuration.basePath = basePath
        }
        this.encoder =
            this.configuration.encoder || new CustomHttpParameterCodec()
    }

    /**
     * @param consumes string[] mime-types
     * @return true: consumes contains 'multipart/form-data', false: otherwise
     */
    private canConsumeForm(consumes: string[]): boolean {
        const form = 'multipart/form-data'
        for (const consume of consumes) {
            if (form === consume) {
                return true
            }
        }
        return false
    }

    // @ts-ignore
    private addToHttpParams(
        httpParams: HttpParams,
        value: any,
        key?: string
    ): HttpParams {
        if (typeof value === 'object' && value instanceof Date === false) {
            httpParams = this.addToHttpParamsRecursive(httpParams, value)
        } else {
            httpParams = this.addToHttpParamsRecursive(httpParams, value, key)
        }
        return httpParams
    }

    private addToHttpParamsRecursive(
        httpParams: HttpParams,
        value?: any,
        key?: string
    ): HttpParams {
        if (value == null) {
            return httpParams
        }

        if (typeof value === 'object') {
            if (Array.isArray(value)) {
                ;(value as any[]).forEach(
                    (elem) =>
                        (httpParams = this.addToHttpParamsRecursive(
                            httpParams,
                            elem,
                            key
                        ))
                )
            } else if (value instanceof Date) {
                if (key != null) {
                    httpParams = httpParams.append(
                        key,
                        (value as Date).toISOString().substring(0, 10)
                    )
                } else {
                    throw Error('key may not be null if value is Date')
                }
            } else {
                Object.keys(value).forEach(
                    (k) =>
                        (httpParams = this.addToHttpParamsRecursive(
                            httpParams,
                            value[k],
                            key != null ? `${key}.${k}` : k
                        ))
                )
            }
        } else if (key != null) {
            httpParams = httpParams.append(key, value)
        } else {
            throw Error('key may not be null if value is not object or array')
        }
        return httpParams
    }

    /**
     * Add file to existing Tender
     * @param tenderId Tender ID
     * @param file
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public addTenderFile(
        tenderId: string,
        file?: Blob,
        observe?: 'body',
        reportProgress?: boolean,
        options?: {
            httpHeaderAccept?: 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<FileMetadataDTO>
    public addTenderFile(
        tenderId: string,
        file?: Blob,
        observe?: 'response',
        reportProgress?: boolean,
        options?: {
            httpHeaderAccept?: 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<HttpResponse<FileMetadataDTO>>
    public addTenderFile(
        tenderId: string,
        file?: Blob,
        observe?: 'events',
        reportProgress?: boolean,
        options?: {
            httpHeaderAccept?: 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<HttpEvent<FileMetadataDTO>>
    public addTenderFile(
        tenderId: string,
        file?: Blob,
        observe: any = 'body',
        reportProgress: boolean = false,
        options?: {
            httpHeaderAccept?: 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<any> {
        if (tenderId === null || tenderId === undefined) {
            throw new Error(
                'Required parameter tenderId was null or undefined when calling addTenderFile.'
            )
        }

        let localVarHeaders = this.defaultHeaders

        let localVarHttpHeaderAcceptSelected: string | undefined =
            options && options.httpHeaderAccept
        if (localVarHttpHeaderAcceptSelected === undefined) {
            // to determine the Accept header
            const httpHeaderAccepts: string[] = ['application/json']
            localVarHttpHeaderAcceptSelected =
                this.configuration.selectHeaderAccept(httpHeaderAccepts)
        }
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set(
                'Accept',
                localVarHttpHeaderAcceptSelected
            )
        }

        let localVarHttpContext: HttpContext | undefined =
            options && options.context
        if (localVarHttpContext === undefined) {
            localVarHttpContext = new HttpContext()
        }

        let localVarTransferCache: boolean | undefined =
            options && options.transferCache
        if (localVarTransferCache === undefined) {
            localVarTransferCache = true
        }

        // to determine the Content-Type header
        const consumes: string[] = ['multipart/form-data']

        const canConsumeForm = this.canConsumeForm(consumes)

        let localVarFormParams: { append(param: string, value: any): any }
        let localVarUseForm = false
        let localVarConvertFormParamsToString = false
        // use FormData to transmit files using content-type "multipart/form-data"
        // see https://stackoverflow.com/questions/4007969/application-x-www-form-urlencoded-or-multipart-form-data
        localVarUseForm = canConsumeForm
        if (localVarUseForm) {
            localVarFormParams = new FormData()
        } else {
            localVarFormParams = new HttpParams({ encoder: this.encoder })
        }

        if (file !== undefined) {
            localVarFormParams =
                (localVarFormParams.append('file', <any>file) as any) ||
                localVarFormParams
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json'
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text'
            } else if (
                this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)
            ) {
                responseType_ = 'json'
            } else {
                responseType_ = 'blob'
            }
        }

        let localVarPath = `/tenders/${this.configuration.encodeParam({ name: 'tenderId', value: tenderId, in: 'path', style: 'simple', explode: false, dataType: 'string', dataFormat: 'uuid' })}/files`
        return this.httpClient.request<FileMetadataDTO>(
            'post',
            `${this.configuration.basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: localVarConvertFormParamsToString
                    ? localVarFormParams.toString()
                    : localVarFormParams,
                responseType: <any>responseType_,
                withCredentials: this.configuration.withCredentials,
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress,
            }
        )
    }

    /**
     * Create a new tender
     * @param title Title of the tender
     * @param client The publisher of the tender
     * @param submissionDate The latest date and time until the offer can be submitted (ISO 8601 date time format)
     * @param bindingDeadline The period of time during which a bidder is legally bound by its bid
     * @param contractDuration Duration of the contract
     * @param publicationDate Date when the tender was published (ISO 8601 date format)
     * @param questionDeadline The question deadline is the last datetime, questions can be sent to the client (ISO 8601 date time format)
     * @param winningCriteria List of criteria we have to deliver to win the tender + optional topics that are relevant (Must have and nice to have criteria)
     * @param weightingPriceQuality The ratio between price and quality which is well described in the tender.
     * @param deliveryLocation Location requirements for the delivery of a tender.
     * @param description Very long general description text
     * @param workflowStatus
     * @param sourceUrl Direct link to the original source of the tender
     * @param contractValue Value of the whole contract, the currency unit is Euro (€)
     * @param maximumBudget The maximum amount of budget, normally listed in \\\&quot;Person-days\\\&quot; (PT)
     * @param comment User comments about the tender
     * @param rating User rating of the tender from 1 to 5
     * @param isFavorite Flag indicating if the tender is marked as a favorite
     * @param files List of documents that belong to the tender and should be uploaded
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public createTender(
        title: string,
        client: string,
        submissionDate: string,
        bindingDeadline: string,
        contractDuration: string,
        publicationDate: string,
        questionDeadline: string,
        winningCriteria: string,
        weightingPriceQuality: string,
        deliveryLocation: string,
        description: string,
        workflowStatus: WorkflowStatusDTO,
        sourceUrl?: string,
        contractValue?: number,
        maximumBudget?: string,
        comment?: string,
        rating?: number,
        isFavorite?: boolean,
        files?: Array<Blob>,
        observe?: 'body',
        reportProgress?: boolean,
        options?: {
            httpHeaderAccept?: 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<TenderDTO>
    public createTender(
        title: string,
        client: string,
        submissionDate: string,
        bindingDeadline: string,
        contractDuration: string,
        publicationDate: string,
        questionDeadline: string,
        winningCriteria: string,
        weightingPriceQuality: string,
        deliveryLocation: string,
        description: string,
        workflowStatus: WorkflowStatusDTO,
        sourceUrl?: string,
        contractValue?: number,
        maximumBudget?: string,
        comment?: string,
        rating?: number,
        isFavorite?: boolean,
        files?: Array<Blob>,
        observe?: 'response',
        reportProgress?: boolean,
        options?: {
            httpHeaderAccept?: 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<HttpResponse<TenderDTO>>
    public createTender(
        title: string,
        client: string,
        submissionDate: string,
        bindingDeadline: string,
        contractDuration: string,
        publicationDate: string,
        questionDeadline: string,
        winningCriteria: string,
        weightingPriceQuality: string,
        deliveryLocation: string,
        description: string,
        workflowStatus: WorkflowStatusDTO,
        sourceUrl?: string,
        contractValue?: number,
        maximumBudget?: string,
        comment?: string,
        rating?: number,
        isFavorite?: boolean,
        files?: Array<Blob>,
        observe?: 'events',
        reportProgress?: boolean,
        options?: {
            httpHeaderAccept?: 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<HttpEvent<TenderDTO>>
    public createTender(
        title: string,
        client: string,
        submissionDate: string,
        bindingDeadline: string,
        contractDuration: string,
        publicationDate: string,
        questionDeadline: string,
        winningCriteria: string,
        weightingPriceQuality: string,
        deliveryLocation: string,
        description: string,
        workflowStatus: WorkflowStatusDTO,
        sourceUrl?: string,
        contractValue?: number,
        maximumBudget?: string,
        comment?: string,
        rating?: number,
        isFavorite?: boolean,
        files?: Array<Blob>,
        observe: any = 'body',
        reportProgress: boolean = false,
        options?: {
            httpHeaderAccept?: 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<any> {
        if (title === null || title === undefined) {
            throw new Error(
                'Required parameter title was null or undefined when calling createTender.'
            )
        }
        if (client === null || client === undefined) {
            throw new Error(
                'Required parameter client was null or undefined when calling createTender.'
            )
        }
        if (submissionDate === null || submissionDate === undefined) {
            throw new Error(
                'Required parameter submissionDate was null or undefined when calling createTender.'
            )
        }
        if (bindingDeadline === null || bindingDeadline === undefined) {
            throw new Error(
                'Required parameter bindingDeadline was null or undefined when calling createTender.'
            )
        }
        if (contractDuration === null || contractDuration === undefined) {
            throw new Error(
                'Required parameter contractDuration was null or undefined when calling createTender.'
            )
        }
        if (publicationDate === null || publicationDate === undefined) {
            throw new Error(
                'Required parameter publicationDate was null or undefined when calling createTender.'
            )
        }
        if (questionDeadline === null || questionDeadline === undefined) {
            throw new Error(
                'Required parameter questionDeadline was null or undefined when calling createTender.'
            )
        }
        if (winningCriteria === null || winningCriteria === undefined) {
            throw new Error(
                'Required parameter winningCriteria was null or undefined when calling createTender.'
            )
        }
        if (
            weightingPriceQuality === null ||
            weightingPriceQuality === undefined
        ) {
            throw new Error(
                'Required parameter weightingPriceQuality was null or undefined when calling createTender.'
            )
        }
        if (deliveryLocation === null || deliveryLocation === undefined) {
            throw new Error(
                'Required parameter deliveryLocation was null or undefined when calling createTender.'
            )
        }
        if (description === null || description === undefined) {
            throw new Error(
                'Required parameter description was null or undefined when calling createTender.'
            )
        }
        if (workflowStatus === null || workflowStatus === undefined) {
            throw new Error(
                'Required parameter workflowStatus was null or undefined when calling createTender.'
            )
        }

        let localVarHeaders = this.defaultHeaders

        let localVarHttpHeaderAcceptSelected: string | undefined =
            options && options.httpHeaderAccept
        if (localVarHttpHeaderAcceptSelected === undefined) {
            // to determine the Accept header
            const httpHeaderAccepts: string[] = ['application/json']
            localVarHttpHeaderAcceptSelected =
                this.configuration.selectHeaderAccept(httpHeaderAccepts)
        }
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set(
                'Accept',
                localVarHttpHeaderAcceptSelected
            )
        }

        let localVarHttpContext: HttpContext | undefined =
            options && options.context
        if (localVarHttpContext === undefined) {
            localVarHttpContext = new HttpContext()
        }

        let localVarTransferCache: boolean | undefined =
            options && options.transferCache
        if (localVarTransferCache === undefined) {
            localVarTransferCache = true
        }

        // to determine the Content-Type header
        const consumes: string[] = ['multipart/form-data']

        const canConsumeForm = this.canConsumeForm(consumes)

        let localVarFormParams: { append(param: string, value: any): any }
        let localVarUseForm = false
        let localVarConvertFormParamsToString = false
        // use FormData to transmit files using content-type "multipart/form-data"
        // see https://stackoverflow.com/questions/4007969/application-x-www-form-urlencoded-or-multipart-form-data
        localVarUseForm = canConsumeForm
        if (localVarUseForm) {
            localVarFormParams = new FormData()
        } else {
            localVarFormParams = new HttpParams({ encoder: this.encoder })
        }

        if (title !== undefined) {
            localVarFormParams =
                (localVarFormParams.append('title', <any>title) as any) ||
                localVarFormParams
        }
        if (sourceUrl !== undefined) {
            localVarFormParams =
                (localVarFormParams.append(
                    'sourceUrl',
                    <any>sourceUrl
                ) as any) || localVarFormParams
        }
        if (client !== undefined) {
            localVarFormParams =
                (localVarFormParams.append('client', <any>client) as any) ||
                localVarFormParams
        }
        if (submissionDate !== undefined) {
            localVarFormParams =
                (localVarFormParams.append(
                    'submissionDate',
                    <any>submissionDate
                ) as any) || localVarFormParams
        }
        if (bindingDeadline !== undefined) {
            localVarFormParams =
                (localVarFormParams.append(
                    'bindingDeadline',
                    <any>bindingDeadline
                ) as any) || localVarFormParams
        }
        if (contractDuration !== undefined) {
            localVarFormParams =
                (localVarFormParams.append(
                    'contractDuration',
                    <any>contractDuration
                ) as any) || localVarFormParams
        }
        if (publicationDate !== undefined) {
            localVarFormParams =
                (localVarFormParams.append(
                    'publicationDate',
                    <any>publicationDate
                ) as any) || localVarFormParams
        }
        if (questionDeadline !== undefined) {
            localVarFormParams =
                (localVarFormParams.append(
                    'questionDeadline',
                    <any>questionDeadline
                ) as any) || localVarFormParams
        }
        if (contractValue !== undefined) {
            localVarFormParams =
                (localVarFormParams.append(
                    'contractValue',
                    <any>contractValue
                ) as any) || localVarFormParams
        }
        if (maximumBudget !== undefined) {
            localVarFormParams =
                (localVarFormParams.append(
                    'maximumBudget',
                    <any>maximumBudget
                ) as any) || localVarFormParams
        }
        if (winningCriteria !== undefined) {
            localVarFormParams =
                (localVarFormParams.append(
                    'winningCriteria',
                    <any>winningCriteria
                ) as any) || localVarFormParams
        }
        if (weightingPriceQuality !== undefined) {
            localVarFormParams =
                (localVarFormParams.append(
                    'weightingPriceQuality',
                    <any>weightingPriceQuality
                ) as any) || localVarFormParams
        }
        if (deliveryLocation !== undefined) {
            localVarFormParams =
                (localVarFormParams.append(
                    'deliveryLocation',
                    <any>deliveryLocation
                ) as any) || localVarFormParams
        }
        if (description !== undefined) {
            localVarFormParams =
                (localVarFormParams.append(
                    'description',
                    <any>description
                ) as any) || localVarFormParams
        }
        if (workflowStatus !== undefined) {
            localVarFormParams =
                (localVarFormParams.append(
                    'workflowStatus',
                    <any>workflowStatus
                ) as any) || localVarFormParams
        }
        if (comment !== undefined) {
            localVarFormParams =
                (localVarFormParams.append('comment', <any>comment) as any) ||
                localVarFormParams
        }
        if (rating !== undefined) {
            localVarFormParams =
                (localVarFormParams.append('rating', <any>rating) as any) ||
                localVarFormParams
        }
        if (isFavorite !== undefined) {
            localVarFormParams =
                (localVarFormParams.append(
                    'isFavorite',
                    <any>isFavorite
                ) as any) || localVarFormParams
        }
        if (files) {
            if (localVarUseForm) {
                files.forEach((element) => {
                    localVarFormParams =
                        (localVarFormParams.append(
                            'files',
                            <any>element
                        ) as any) || localVarFormParams
                })
            } else {
                localVarFormParams =
                    (localVarFormParams.append(
                        'files',
                        [...files].join(COLLECTION_FORMATS['csv'])
                    ) as any) || localVarFormParams
            }
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json'
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text'
            } else if (
                this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)
            ) {
                responseType_ = 'json'
            } else {
                responseType_ = 'blob'
            }
        }

        let localVarPath = `/tenders`
        return this.httpClient.request<TenderDTO>(
            'post',
            `${this.configuration.basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: localVarConvertFormParamsToString
                    ? localVarFormParams.toString()
                    : localVarFormParams,
                responseType: <any>responseType_,
                withCredentials: this.configuration.withCredentials,
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress,
            }
        )
    }

    /**
     * Create a new tender with AI assistance to extract missing fields
     * @param title Title of the tender
     * @param description Description text that will be analyzed by AI to extract missing fields
     * @param sourceUrl Direct link to the original source of the tender
     * @param files List of documents that will be analyzed to extract tender information
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public createTenderWithAI(
        title: string,
        description: string,
        sourceUrl?: string,
        files?: Array<Blob>,
        observe?: 'body',
        reportProgress?: boolean,
        options?: {
            httpHeaderAccept?: 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<TenderDTO>
    public createTenderWithAI(
        title: string,
        description: string,
        sourceUrl?: string,
        files?: Array<Blob>,
        observe?: 'response',
        reportProgress?: boolean,
        options?: {
            httpHeaderAccept?: 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<HttpResponse<TenderDTO>>
    public createTenderWithAI(
        title: string,
        description: string,
        sourceUrl?: string,
        files?: Array<Blob>,
        observe?: 'events',
        reportProgress?: boolean,
        options?: {
            httpHeaderAccept?: 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<HttpEvent<TenderDTO>>
    public createTenderWithAI(
        title: string,
        description: string,
        sourceUrl?: string,
        files?: Array<Blob>,
        observe: any = 'body',
        reportProgress: boolean = false,
        options?: {
            httpHeaderAccept?: 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<any> {
        if (title === null || title === undefined) {
            throw new Error(
                'Required parameter title was null or undefined when calling createTenderWithAI.'
            )
        }
        if (description === null || description === undefined) {
            throw new Error(
                'Required parameter description was null or undefined when calling createTenderWithAI.'
            )
        }

        let localVarHeaders = this.defaultHeaders

        let localVarHttpHeaderAcceptSelected: string | undefined =
            options && options.httpHeaderAccept
        if (localVarHttpHeaderAcceptSelected === undefined) {
            // to determine the Accept header
            const httpHeaderAccepts: string[] = ['application/json']
            localVarHttpHeaderAcceptSelected =
                this.configuration.selectHeaderAccept(httpHeaderAccepts)
        }
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set(
                'Accept',
                localVarHttpHeaderAcceptSelected
            )
        }

        let localVarHttpContext: HttpContext | undefined =
            options && options.context
        if (localVarHttpContext === undefined) {
            localVarHttpContext = new HttpContext()
        }

        let localVarTransferCache: boolean | undefined =
            options && options.transferCache
        if (localVarTransferCache === undefined) {
            localVarTransferCache = true
        }

        // to determine the Content-Type header
        const consumes: string[] = ['multipart/form-data']

        const canConsumeForm = this.canConsumeForm(consumes)

        let localVarFormParams: { append(param: string, value: any): any }
        let localVarUseForm = false
        let localVarConvertFormParamsToString = false
        // use FormData to transmit files using content-type "multipart/form-data"
        // see https://stackoverflow.com/questions/4007969/application-x-www-form-urlencoded-or-multipart-form-data
        localVarUseForm = canConsumeForm
        if (localVarUseForm) {
            localVarFormParams = new FormData()
        } else {
            localVarFormParams = new HttpParams({ encoder: this.encoder })
        }

        if (title !== undefined) {
            localVarFormParams =
                (localVarFormParams.append('title', <any>title) as any) ||
                localVarFormParams
        }
        if (sourceUrl !== undefined) {
            localVarFormParams =
                (localVarFormParams.append(
                    'sourceUrl',
                    <any>sourceUrl
                ) as any) || localVarFormParams
        }
        if (description !== undefined) {
            localVarFormParams =
                (localVarFormParams.append(
                    'description',
                    <any>description
                ) as any) || localVarFormParams
        }
        if (files) {
            if (localVarUseForm) {
                files.forEach((element) => {
                    localVarFormParams =
                        (localVarFormParams.append(
                            'files',
                            <any>element
                        ) as any) || localVarFormParams
                })
            } else {
                localVarFormParams =
                    (localVarFormParams.append(
                        'files',
                        [...files].join(COLLECTION_FORMATS['csv'])
                    ) as any) || localVarFormParams
            }
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json'
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text'
            } else if (
                this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)
            ) {
                responseType_ = 'json'
            } else {
                responseType_ = 'blob'
            }
        }

        let localVarPath = `/tenders/create-with-ai`
        return this.httpClient.request<TenderDTO>(
            'post',
            `${this.configuration.basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: localVarConvertFormParamsToString
                    ? localVarFormParams.toString()
                    : localVarFormParams,
                responseType: <any>responseType_,
                withCredentials: this.configuration.withCredentials,
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress,
            }
        )
    }

    /**
     * Delete specific tender
     * @param id Tender ID
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public deleteTender(
        id: string,
        observe?: 'body',
        reportProgress?: boolean,
        options?: {
            httpHeaderAccept?: 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<any>
    public deleteTender(
        id: string,
        observe?: 'response',
        reportProgress?: boolean,
        options?: {
            httpHeaderAccept?: 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<HttpResponse<any>>
    public deleteTender(
        id: string,
        observe?: 'events',
        reportProgress?: boolean,
        options?: {
            httpHeaderAccept?: 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<HttpEvent<any>>
    public deleteTender(
        id: string,
        observe: any = 'body',
        reportProgress: boolean = false,
        options?: {
            httpHeaderAccept?: 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<any> {
        if (id === null || id === undefined) {
            throw new Error(
                'Required parameter id was null or undefined when calling deleteTender.'
            )
        }

        let localVarHeaders = this.defaultHeaders

        let localVarHttpHeaderAcceptSelected: string | undefined =
            options && options.httpHeaderAccept
        if (localVarHttpHeaderAcceptSelected === undefined) {
            // to determine the Accept header
            const httpHeaderAccepts: string[] = ['application/json']
            localVarHttpHeaderAcceptSelected =
                this.configuration.selectHeaderAccept(httpHeaderAccepts)
        }
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set(
                'Accept',
                localVarHttpHeaderAcceptSelected
            )
        }

        let localVarHttpContext: HttpContext | undefined =
            options && options.context
        if (localVarHttpContext === undefined) {
            localVarHttpContext = new HttpContext()
        }

        let localVarTransferCache: boolean | undefined =
            options && options.transferCache
        if (localVarTransferCache === undefined) {
            localVarTransferCache = true
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json'
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text'
            } else if (
                this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)
            ) {
                responseType_ = 'json'
            } else {
                responseType_ = 'blob'
            }
        }

        let localVarPath = `/tenders/${this.configuration.encodeParam({ name: 'id', value: id, in: 'path', style: 'simple', explode: false, dataType: 'string', dataFormat: 'uuid' })}`
        return this.httpClient.request<any>(
            'delete',
            `${this.configuration.basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                responseType: <any>responseType_,
                withCredentials: this.configuration.withCredentials,
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress,
            }
        )
    }

    /**
     * Delete specific file of a tender
     * @param tenderId Tender ID
     * @param fileId File ID
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public deleteTenderFile(
        tenderId: string,
        fileId: string,
        observe?: 'body',
        reportProgress?: boolean,
        options?: {
            httpHeaderAccept?: 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<any>
    public deleteTenderFile(
        tenderId: string,
        fileId: string,
        observe?: 'response',
        reportProgress?: boolean,
        options?: {
            httpHeaderAccept?: 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<HttpResponse<any>>
    public deleteTenderFile(
        tenderId: string,
        fileId: string,
        observe?: 'events',
        reportProgress?: boolean,
        options?: {
            httpHeaderAccept?: 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<HttpEvent<any>>
    public deleteTenderFile(
        tenderId: string,
        fileId: string,
        observe: any = 'body',
        reportProgress: boolean = false,
        options?: {
            httpHeaderAccept?: 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<any> {
        if (tenderId === null || tenderId === undefined) {
            throw new Error(
                'Required parameter tenderId was null or undefined when calling deleteTenderFile.'
            )
        }
        if (fileId === null || fileId === undefined) {
            throw new Error(
                'Required parameter fileId was null or undefined when calling deleteTenderFile.'
            )
        }

        let localVarHeaders = this.defaultHeaders

        let localVarHttpHeaderAcceptSelected: string | undefined =
            options && options.httpHeaderAccept
        if (localVarHttpHeaderAcceptSelected === undefined) {
            // to determine the Accept header
            const httpHeaderAccepts: string[] = ['application/json']
            localVarHttpHeaderAcceptSelected =
                this.configuration.selectHeaderAccept(httpHeaderAccepts)
        }
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set(
                'Accept',
                localVarHttpHeaderAcceptSelected
            )
        }

        let localVarHttpContext: HttpContext | undefined =
            options && options.context
        if (localVarHttpContext === undefined) {
            localVarHttpContext = new HttpContext()
        }

        let localVarTransferCache: boolean | undefined =
            options && options.transferCache
        if (localVarTransferCache === undefined) {
            localVarTransferCache = true
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json'
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text'
            } else if (
                this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)
            ) {
                responseType_ = 'json'
            } else {
                responseType_ = 'blob'
            }
        }

        let localVarPath = `/tenders/${this.configuration.encodeParam({ name: 'tenderId', value: tenderId, in: 'path', style: 'simple', explode: false, dataType: 'string', dataFormat: 'uuid' })}/files/${this.configuration.encodeParam({ name: 'fileId', value: fileId, in: 'path', style: 'simple', explode: false, dataType: 'string', dataFormat: 'uuid' })}`
        return this.httpClient.request<any>(
            'delete',
            `${this.configuration.basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                responseType: <any>responseType_,
                withCredentials: this.configuration.withCredentials,
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress,
            }
        )
    }

    /**
     * Download specific file of a tender
     * @param tenderId Tender ID
     * @param fileId File ID
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public downloadTenderFile(
        tenderId: string,
        fileId: string,
        observe?: 'body',
        reportProgress?: boolean,
        options?: {
            httpHeaderAccept?: 'application/octet-stream' | 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<Blob>
    public downloadTenderFile(
        tenderId: string,
        fileId: string,
        observe?: 'response',
        reportProgress?: boolean,
        options?: {
            httpHeaderAccept?: 'application/octet-stream' | 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<HttpResponse<Blob>>
    public downloadTenderFile(
        tenderId: string,
        fileId: string,
        observe?: 'events',
        reportProgress?: boolean,
        options?: {
            httpHeaderAccept?: 'application/octet-stream' | 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<HttpEvent<Blob>>
    public downloadTenderFile(
        tenderId: string,
        fileId: string,
        observe: any = 'body',
        reportProgress: boolean = false,
        options?: {
            httpHeaderAccept?: 'application/octet-stream' | 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<any> {
        if (tenderId === null || tenderId === undefined) {
            throw new Error(
                'Required parameter tenderId was null or undefined when calling downloadTenderFile.'
            )
        }
        if (fileId === null || fileId === undefined) {
            throw new Error(
                'Required parameter fileId was null or undefined when calling downloadTenderFile.'
            )
        }

        let localVarHeaders = this.defaultHeaders

        let localVarHttpHeaderAcceptSelected: string | undefined =
            options && options.httpHeaderAccept
        if (localVarHttpHeaderAcceptSelected === undefined) {
            // to determine the Accept header
            const httpHeaderAccepts: string[] = [
                'application/octet-stream',
                'application/json',
            ]
            localVarHttpHeaderAcceptSelected =
                this.configuration.selectHeaderAccept(httpHeaderAccepts)
        }
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set(
                'Accept',
                localVarHttpHeaderAcceptSelected
            )
        }

        let localVarHttpContext: HttpContext | undefined =
            options && options.context
        if (localVarHttpContext === undefined) {
            localVarHttpContext = new HttpContext()
        }

        let localVarTransferCache: boolean | undefined =
            options && options.transferCache
        if (localVarTransferCache === undefined) {
            localVarTransferCache = true
        }

        let localVarPath = `/tenders/${this.configuration.encodeParam({ name: 'tenderId', value: tenderId, in: 'path', style: 'simple', explode: false, dataType: 'string', dataFormat: 'uuid' })}/files/${this.configuration.encodeParam({ name: 'fileId', value: fileId, in: 'path', style: 'simple', explode: false, dataType: 'string', dataFormat: 'uuid' })}`
        return this.httpClient.request(
            'get',
            `${this.configuration.basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                responseType: 'blob',
                withCredentials: this.configuration.withCredentials,
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress,
            }
        )
    }

    /**
     * Retrieve all existing tenders
     * @param page
     * @param size
     * @param sortBy Name of the property the collection results should be sorted by. Only the properties in the enum are supported at the moment.
     * @param sortDirection Sort order
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public getAllTenders(
        page: number,
        size: number,
        sortBy?: TenderSortableFieldsDTO,
        sortDirection?: CollectionSortDirectionDTO,
        observe?: 'body',
        reportProgress?: boolean,
        options?: {
            httpHeaderAccept?: 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<TenderPagedDTO>
    public getAllTenders(
        page: number,
        size: number,
        sortBy?: TenderSortableFieldsDTO,
        sortDirection?: CollectionSortDirectionDTO,
        observe?: 'response',
        reportProgress?: boolean,
        options?: {
            httpHeaderAccept?: 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<HttpResponse<TenderPagedDTO>>
    public getAllTenders(
        page: number,
        size: number,
        sortBy?: TenderSortableFieldsDTO,
        sortDirection?: CollectionSortDirectionDTO,
        observe?: 'events',
        reportProgress?: boolean,
        options?: {
            httpHeaderAccept?: 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<HttpEvent<TenderPagedDTO>>
    public getAllTenders(
        page: number,
        size: number,
        sortBy?: TenderSortableFieldsDTO,
        sortDirection?: CollectionSortDirectionDTO,
        observe: any = 'body',
        reportProgress: boolean = false,
        options?: {
            httpHeaderAccept?: 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<any> {
        if (page === null || page === undefined) {
            throw new Error(
                'Required parameter page was null or undefined when calling getAllTenders.'
            )
        }
        if (size === null || size === undefined) {
            throw new Error(
                'Required parameter size was null or undefined when calling getAllTenders.'
            )
        }

        let localVarQueryParameters = new HttpParams({ encoder: this.encoder })
        if (page !== undefined && page !== null) {
            localVarQueryParameters = this.addToHttpParams(
                localVarQueryParameters,
                <any>page,
                'page'
            )
        }
        if (size !== undefined && size !== null) {
            localVarQueryParameters = this.addToHttpParams(
                localVarQueryParameters,
                <any>size,
                'size'
            )
        }
        if (sortBy !== undefined && sortBy !== null) {
            localVarQueryParameters = this.addToHttpParams(
                localVarQueryParameters,
                <any>sortBy,
                'sortBy'
            )
        }
        if (sortDirection !== undefined && sortDirection !== null) {
            localVarQueryParameters = this.addToHttpParams(
                localVarQueryParameters,
                <any>sortDirection,
                'sortDirection'
            )
        }

        let localVarHeaders = this.defaultHeaders

        let localVarHttpHeaderAcceptSelected: string | undefined =
            options && options.httpHeaderAccept
        if (localVarHttpHeaderAcceptSelected === undefined) {
            // to determine the Accept header
            const httpHeaderAccepts: string[] = ['application/json']
            localVarHttpHeaderAcceptSelected =
                this.configuration.selectHeaderAccept(httpHeaderAccepts)
        }
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set(
                'Accept',
                localVarHttpHeaderAcceptSelected
            )
        }

        let localVarHttpContext: HttpContext | undefined =
            options && options.context
        if (localVarHttpContext === undefined) {
            localVarHttpContext = new HttpContext()
        }

        let localVarTransferCache: boolean | undefined =
            options && options.transferCache
        if (localVarTransferCache === undefined) {
            localVarTransferCache = true
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json'
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text'
            } else if (
                this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)
            ) {
                responseType_ = 'json'
            } else {
                responseType_ = 'blob'
            }
        }

        let localVarPath = `/tenders`
        return this.httpClient.request<TenderPagedDTO>(
            'get',
            `${this.configuration.basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                params: localVarQueryParameters,
                responseType: <any>responseType_,
                withCredentials: this.configuration.withCredentials,
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress,
            }
        )
    }

    /**
     * Retrieve specific tender
     * @param id Tender ID
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public getTenderById(
        id: string,
        observe?: 'body',
        reportProgress?: boolean,
        options?: {
            httpHeaderAccept?: 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<TenderDTO>
    public getTenderById(
        id: string,
        observe?: 'response',
        reportProgress?: boolean,
        options?: {
            httpHeaderAccept?: 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<HttpResponse<TenderDTO>>
    public getTenderById(
        id: string,
        observe?: 'events',
        reportProgress?: boolean,
        options?: {
            httpHeaderAccept?: 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<HttpEvent<TenderDTO>>
    public getTenderById(
        id: string,
        observe: any = 'body',
        reportProgress: boolean = false,
        options?: {
            httpHeaderAccept?: 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<any> {
        if (id === null || id === undefined) {
            throw new Error(
                'Required parameter id was null or undefined when calling getTenderById.'
            )
        }

        let localVarHeaders = this.defaultHeaders

        let localVarHttpHeaderAcceptSelected: string | undefined =
            options && options.httpHeaderAccept
        if (localVarHttpHeaderAcceptSelected === undefined) {
            // to determine the Accept header
            const httpHeaderAccepts: string[] = ['application/json']
            localVarHttpHeaderAcceptSelected =
                this.configuration.selectHeaderAccept(httpHeaderAccepts)
        }
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set(
                'Accept',
                localVarHttpHeaderAcceptSelected
            )
        }

        let localVarHttpContext: HttpContext | undefined =
            options && options.context
        if (localVarHttpContext === undefined) {
            localVarHttpContext = new HttpContext()
        }

        let localVarTransferCache: boolean | undefined =
            options && options.transferCache
        if (localVarTransferCache === undefined) {
            localVarTransferCache = true
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json'
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text'
            } else if (
                this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)
            ) {
                responseType_ = 'json'
            } else {
                responseType_ = 'blob'
            }
        }

        let localVarPath = `/tenders/${this.configuration.encodeParam({ name: 'id', value: id, in: 'path', style: 'simple', explode: false, dataType: 'string', dataFormat: 'uuid' })}`
        return this.httpClient.request<TenderDTO>(
            'get',
            `${this.configuration.basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                responseType: <any>responseType_,
                withCredentials: this.configuration.withCredentials,
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress,
            }
        )
    }

    /**
     * Update specific tender
     * @param id Tender ID
     * @param tenderUpdateDTO
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public updateTender(
        id: string,
        tenderUpdateDTO: TenderUpdateDTO,
        observe?: 'body',
        reportProgress?: boolean,
        options?: {
            httpHeaderAccept?: 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<TenderDTO>
    public updateTender(
        id: string,
        tenderUpdateDTO: TenderUpdateDTO,
        observe?: 'response',
        reportProgress?: boolean,
        options?: {
            httpHeaderAccept?: 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<HttpResponse<TenderDTO>>
    public updateTender(
        id: string,
        tenderUpdateDTO: TenderUpdateDTO,
        observe?: 'events',
        reportProgress?: boolean,
        options?: {
            httpHeaderAccept?: 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<HttpEvent<TenderDTO>>
    public updateTender(
        id: string,
        tenderUpdateDTO: TenderUpdateDTO,
        observe: any = 'body',
        reportProgress: boolean = false,
        options?: {
            httpHeaderAccept?: 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<any> {
        if (id === null || id === undefined) {
            throw new Error(
                'Required parameter id was null or undefined when calling updateTender.'
            )
        }
        if (tenderUpdateDTO === null || tenderUpdateDTO === undefined) {
            throw new Error(
                'Required parameter tenderUpdateDTO was null or undefined when calling updateTender.'
            )
        }

        let localVarHeaders = this.defaultHeaders

        let localVarHttpHeaderAcceptSelected: string | undefined =
            options && options.httpHeaderAccept
        if (localVarHttpHeaderAcceptSelected === undefined) {
            // to determine the Accept header
            const httpHeaderAccepts: string[] = ['application/json']
            localVarHttpHeaderAcceptSelected =
                this.configuration.selectHeaderAccept(httpHeaderAccepts)
        }
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set(
                'Accept',
                localVarHttpHeaderAcceptSelected
            )
        }

        let localVarHttpContext: HttpContext | undefined =
            options && options.context
        if (localVarHttpContext === undefined) {
            localVarHttpContext = new HttpContext()
        }

        let localVarTransferCache: boolean | undefined =
            options && options.transferCache
        if (localVarTransferCache === undefined) {
            localVarTransferCache = true
        }

        // to determine the Content-Type header
        const consumes: string[] = ['application/json']
        const httpContentTypeSelected: string | undefined =
            this.configuration.selectHeaderContentType(consumes)
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set(
                'Content-Type',
                httpContentTypeSelected
            )
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json'
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text'
            } else if (
                this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)
            ) {
                responseType_ = 'json'
            } else {
                responseType_ = 'blob'
            }
        }

        let localVarPath = `/tenders/${this.configuration.encodeParam({ name: 'id', value: id, in: 'path', style: 'simple', explode: false, dataType: 'string', dataFormat: 'uuid' })}`
        return this.httpClient.request<TenderDTO>(
            'put',
            `${this.configuration.basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: tenderUpdateDTO,
                responseType: <any>responseType_,
                withCredentials: this.configuration.withCredentials,
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress,
            }
        )
    }
}
