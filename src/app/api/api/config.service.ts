/**
 * AI Tender Analysis - REST API
 *
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Inject, Injectable, Optional } from '@angular/core'
import {
    HttpClient,
    HttpHeaders,
    HttpParams,
    HttpResponse,
    HttpEvent,
    HttpParameterCodec,
    HttpContext,
} from '@angular/common/http'
import { CustomHttpParameterCodec } from '../encoder'
import { Observable } from 'rxjs'

// @ts-ignore
import { ErrorDTO } from '../model/errorDTO'
// @ts-ignore
import { SystemConfigurationDTO } from '../model/systemConfigurationDTO'

// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS } from '../variables'
import { Configuration } from '../configuration'

@Injectable({
    providedIn: 'root',
})
export class ConfigApiService {
    protected basePath = 'http://localhost'
    public defaultHeaders = new HttpHeaders()
    public configuration = new Configuration()
    public encoder: HttpParameterCodec

    constructor(
        protected httpClient: HttpClient,
        @Optional() @Inject(BASE_PATH) basePath: string | string[],
        @Optional() configuration: Configuration
    ) {
        if (configuration) {
            this.configuration = configuration
        }
        if (typeof this.configuration.basePath !== 'string') {
            const firstBasePath = Array.isArray(basePath)
                ? basePath[0]
                : undefined
            if (firstBasePath != undefined) {
                basePath = firstBasePath
            }

            if (typeof basePath !== 'string') {
                basePath = this.basePath
            }
            this.configuration.basePath = basePath
        }
        this.encoder =
            this.configuration.encoder || new CustomHttpParameterCodec()
    }

    // @ts-ignore
    private addToHttpParams(
        httpParams: HttpParams,
        value: any,
        key?: string
    ): HttpParams {
        if (typeof value === 'object' && value instanceof Date === false) {
            httpParams = this.addToHttpParamsRecursive(httpParams, value)
        } else {
            httpParams = this.addToHttpParamsRecursive(httpParams, value, key)
        }
        return httpParams
    }

    private addToHttpParamsRecursive(
        httpParams: HttpParams,
        value?: any,
        key?: string
    ): HttpParams {
        if (value == null) {
            return httpParams
        }

        if (typeof value === 'object') {
            if (Array.isArray(value)) {
                ;(value as any[]).forEach(
                    (elem) =>
                        (httpParams = this.addToHttpParamsRecursive(
                            httpParams,
                            elem,
                            key
                        ))
                )
            } else if (value instanceof Date) {
                if (key != null) {
                    httpParams = httpParams.append(
                        key,
                        (value as Date).toISOString().substring(0, 10)
                    )
                } else {
                    throw Error('key may not be null if value is Date')
                }
            } else {
                Object.keys(value).forEach(
                    (k) =>
                        (httpParams = this.addToHttpParamsRecursive(
                            httpParams,
                            value[k],
                            key != null ? `${key}.${k}` : k
                        ))
                )
            }
        } else if (key != null) {
            httpParams = httpParams.append(key, value)
        } else {
            throw Error('key may not be null if value is not object or array')
        }
        return httpParams
    }

    /**
     * Retrieve system configuration
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public getSystemConfiguration(
        observe?: 'body',
        reportProgress?: boolean,
        options?: {
            httpHeaderAccept?: 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<SystemConfigurationDTO>
    public getSystemConfiguration(
        observe?: 'response',
        reportProgress?: boolean,
        options?: {
            httpHeaderAccept?: 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<HttpResponse<SystemConfigurationDTO>>
    public getSystemConfiguration(
        observe?: 'events',
        reportProgress?: boolean,
        options?: {
            httpHeaderAccept?: 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<HttpEvent<SystemConfigurationDTO>>
    public getSystemConfiguration(
        observe: any = 'body',
        reportProgress: boolean = false,
        options?: {
            httpHeaderAccept?: 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<any> {
        let localVarHeaders = this.defaultHeaders

        let localVarHttpHeaderAcceptSelected: string | undefined =
            options && options.httpHeaderAccept
        if (localVarHttpHeaderAcceptSelected === undefined) {
            // to determine the Accept header
            const httpHeaderAccepts: string[] = ['application/json']
            localVarHttpHeaderAcceptSelected =
                this.configuration.selectHeaderAccept(httpHeaderAccepts)
        }
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set(
                'Accept',
                localVarHttpHeaderAcceptSelected
            )
        }

        let localVarHttpContext: HttpContext | undefined =
            options && options.context
        if (localVarHttpContext === undefined) {
            localVarHttpContext = new HttpContext()
        }

        let localVarTransferCache: boolean | undefined =
            options && options.transferCache
        if (localVarTransferCache === undefined) {
            localVarTransferCache = true
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json'
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text'
            } else if (
                this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)
            ) {
                responseType_ = 'json'
            } else {
                responseType_ = 'blob'
            }
        }

        let localVarPath = `/config`
        return this.httpClient.request<SystemConfigurationDTO>(
            'get',
            `${this.configuration.basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                responseType: <any>responseType_,
                withCredentials: this.configuration.withCredentials,
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress,
            }
        )
    }

    /**
     * Update system configuration
     * @param systemConfigurationDTO
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public updateSystemConfiguration(
        systemConfigurationDTO: SystemConfigurationDTO,
        observe?: 'body',
        reportProgress?: boolean,
        options?: {
            httpHeaderAccept?: 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<SystemConfigurationDTO>
    public updateSystemConfiguration(
        systemConfigurationDTO: SystemConfigurationDTO,
        observe?: 'response',
        reportProgress?: boolean,
        options?: {
            httpHeaderAccept?: 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<HttpResponse<SystemConfigurationDTO>>
    public updateSystemConfiguration(
        systemConfigurationDTO: SystemConfigurationDTO,
        observe?: 'events',
        reportProgress?: boolean,
        options?: {
            httpHeaderAccept?: 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<HttpEvent<SystemConfigurationDTO>>
    public updateSystemConfiguration(
        systemConfigurationDTO: SystemConfigurationDTO,
        observe: any = 'body',
        reportProgress: boolean = false,
        options?: {
            httpHeaderAccept?: 'application/json'
            context?: HttpContext
            transferCache?: boolean
        }
    ): Observable<any> {
        if (
            systemConfigurationDTO === null ||
            systemConfigurationDTO === undefined
        ) {
            throw new Error(
                'Required parameter systemConfigurationDTO was null or undefined when calling updateSystemConfiguration.'
            )
        }

        let localVarHeaders = this.defaultHeaders

        let localVarHttpHeaderAcceptSelected: string | undefined =
            options && options.httpHeaderAccept
        if (localVarHttpHeaderAcceptSelected === undefined) {
            // to determine the Accept header
            const httpHeaderAccepts: string[] = ['application/json']
            localVarHttpHeaderAcceptSelected =
                this.configuration.selectHeaderAccept(httpHeaderAccepts)
        }
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set(
                'Accept',
                localVarHttpHeaderAcceptSelected
            )
        }

        let localVarHttpContext: HttpContext | undefined =
            options && options.context
        if (localVarHttpContext === undefined) {
            localVarHttpContext = new HttpContext()
        }

        let localVarTransferCache: boolean | undefined =
            options && options.transferCache
        if (localVarTransferCache === undefined) {
            localVarTransferCache = true
        }

        // to determine the Content-Type header
        const consumes: string[] = ['application/json']
        const httpContentTypeSelected: string | undefined =
            this.configuration.selectHeaderContentType(consumes)
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set(
                'Content-Type',
                httpContentTypeSelected
            )
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json'
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text'
            } else if (
                this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)
            ) {
                responseType_ = 'json'
            } else {
                responseType_ = 'blob'
            }
        }

        let localVarPath = `/config`
        return this.httpClient.request<SystemConfigurationDTO>(
            'put',
            `${this.configuration.basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: systemConfigurationDTO,
                responseType: <any>responseType_,
                withCredentials: this.configuration.withCredentials,
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress,
            }
        )
    }
}
